from dblp.api import (
    search_author,
    search_pub,
    add_ccf_attrs,
    add_ccf_class,
    get_ccf_line,
    fetch_author_profile,
    AuthorProfile,
    Author,
    Publication,
    Article,
    InProceedings
)

__all__ = [
    'search_author',
    'search_pub',
    'add_ccf_attrs',
    'add_ccf_class',
    'get_ccf_line',
    'fetch_author_profile',
    'AuthorProfile',
    'Author',
    'Publication',
    'Article',
    'InProceedings'
]

# For backward compatibility, provide a search alias
search = search_pub
