import requests
import pandas as pd
import xml.etree.ElementTree as ET
from dataclasses import dataclass
from typing import List, Optional, Union

from urllib.parse import urlencode
from importlib.resources import open_binary

BASE_URL = 'https://dblp.org/search/%s/api'


def add_ccf_class(results: list[dict]) -> list[dict]:
    def get_ccf_class(venue: str | None, catalog: pd.DataFrame) -> str | None:
        if venue is None:
            return None
        if len(series := catalog.loc[catalog.get('abbr').str.lower() == venue.lower(), 'class']) > 0:
            return series.item()
        if len(series := catalog.loc[catalog.get('url').str.contains(f'/{venue.lower()}/'), 'class']) > 0:
            return series.item()
        return None

    catalog = pd.read_csv(open_binary('dblp.data', 'ccf_catalog.csv'))
    for result in results:
        result['ccf_class'] = get_ccf_class(result.get('venue'), catalog=catalog)
    return results

_catalog = None
def _get_catalog() -> pd.DataFrame:
    global _catalog
    if _catalog is None:
        _catalog = pd.read_csv(open_binary('dblp.data', 'ccf_catalog.csv'))
    return _catalog

def get_ccf_line(venue: str | None) -> dict[str, str] | None:
    if venue is None:
        return None
    catalog = _get_catalog()
    if len(series := catalog.loc[catalog.get('abbr').str.lower() == venue.lower()]) > 0:
        return series.iloc[0].to_dict()
    if len(series := catalog.loc[catalog.get('url').str.contains(f'/{venue.lower()}/')]) > 0:
        return series.iloc[0].to_dict()
    return None

def add_ccf_attrs(results: list[dict], attrs: list[str] | str) -> list[dict]:
    if isinstance(attrs, str):
        attrs = [attrs]
    for result in results:
        line = get_ccf_line(result.get('venue'))
        if line is not None:
            for attr in attrs:
                result[attr] = line.get(attr)
    return results

def search_pub(queries: list[str]) -> list[dict | None]:
    api = BASE_URL % 'publ'

    if isinstance(queries, str):
        queries = [queries]
    results = []
    for query in queries:
        options = {
            'q': query,
            'format': 'json',
            'h': 1,
        }
        r = requests.get(f'{api}?{urlencode(options)}').json()
        hit = r['result']['hits'].get('hit')
        if hit is not None:
            info = hit[0].get('info')
            info['authors'] = [author['text'] for author in info['authors']['author']]
            results.append(info)
        else:
            results.append(None)
    return results

def search_author(queries: list[str] | str) -> list[dict | None]:
    api = BASE_URL % 'author'

    if isinstance(queries, str):
        queries = [queries]
    results = []
    for query in queries:
        options = {
            'q': query,
            'format': 'json',
            'h': 1,
        }
        r = requests.get(f'{api}?{urlencode(options)}').json()
        hit = r['result']['hits'].get('hit')
        if hit is not None:
            info = hit[0].get('info')
            results.append(info)
        else:
            results.append(None)
    return results

@dataclass
class Author:
    """Represents an author with name and optional DBLP person ID."""
    name: str
    pid: Optional[str] = None

@dataclass
class Publication:
    """Base class for publications."""
    key: str
    title: str
    authors: List[Author]
    year: int
    urls: List[str]
    mdate: Optional[str] = None

@dataclass
class Article(Publication):
    """Represents a journal article."""
    journal: str = ""
    volume: Optional[str] = None
    number: Optional[str] = None
    pages: Optional[str] = None
    publtype: Optional[str] = None

@dataclass
class InProceedings(Publication):
    """Represents a conference paper."""
    booktitle: str = ""
    pages: Optional[str] = None
    crossref: Optional[str] = None

@dataclass
class AuthorProfile:
    """Represents a complete author profile from DBLP."""
    name: str
    pid: str
    urls: List[str]
    affiliations: List[str]
    awards: List[dict]
    publications: List[Publication]
    publication_count: int

    def get_articles(self) -> List[Article]:
        """Get all journal articles."""
        return [pub for pub in self.publications if isinstance(pub, Article)]

    def get_conference_papers(self) -> List[InProceedings]:
        """Get all conference papers."""
        return [pub for pub in self.publications if isinstance(pub, InProceedings)]

    def get_publications_by_year(self, year: int) -> List[Publication]:
        """Get all publications from a specific year."""
        return [pub for pub in self.publications if pub.year == year]

def _parse_author_from_xml(author_elem) -> Author:
    """Parse an author element from XML."""
    name = author_elem.text or ""
    pid = author_elem.get('pid')
    return Author(name=name, pid=pid)

def _parse_publication_from_xml(r_elem) -> Optional[Publication]:
    """Parse a publication from an <r> element."""
    # Get the publication element (article or inproceedings)
    pub_elem = None
    for child in r_elem:
        if child.tag in ['article', 'inproceedings']:
            pub_elem = child
            break

    if pub_elem is None:
        return None

    # Extract common fields
    key = pub_elem.get('key', '')
    mdate = pub_elem.get('mdate')
    title = ''
    authors = []
    year = 0
    urls = []

    for elem in pub_elem:
        if elem.tag == 'title':
            title = elem.text or ''
        elif elem.tag == 'author':
            authors.append(_parse_author_from_xml(elem))
        elif elem.tag == 'year':
            try:
                year = int(elem.text or 0)
            except ValueError:
                year = 0
        elif elem.tag == 'ee':
            if elem.text:
                urls.append(elem.text)
        elif elem.tag == 'url':
            if elem.text:
                urls.append(f"https://dblp.org/{elem.text}")

    # Create specific publication type
    if pub_elem.tag == 'article':
        journal = ''
        volume = None
        number = None
        pages = None
        publtype = pub_elem.get('publtype')

        for elem in pub_elem:
            if elem.tag == 'journal':
                journal = elem.text or ''
            elif elem.tag == 'volume':
                volume = elem.text
            elif elem.tag == 'number':
                number = elem.text
            elif elem.tag == 'pages':
                pages = elem.text

        return Article(
            key=key, title=title, authors=authors, year=year, urls=urls,
            mdate=mdate, journal=journal, volume=volume, number=number,
            pages=pages, publtype=publtype
        )

    elif pub_elem.tag == 'inproceedings':
        booktitle = ''
        pages = None
        crossref = None

        for elem in pub_elem:
            if elem.tag == 'booktitle':
                booktitle = elem.text or ''
            elif elem.tag == 'pages':
                pages = elem.text
            elif elem.tag == 'crossref':
                crossref = elem.text

        return InProceedings(
            key=key, title=title, authors=authors, year=year, urls=urls,
            mdate=mdate, booktitle=booktitle, pages=pages, crossref=crossref
        )

    return None

def fetch_author_profile(url: str) -> AuthorProfile:
    """
    Fetch author profile from DBLP.

    Args:
        url: DBLP author page URL (e.g., https://dblp.org/pid/10/3248)

    Returns:
        AuthorProfile object containing author information and publications

    Raises:
        requests.RequestException: If HTTP request fails
        ET.ParseError: If XML parsing fails
        ValueError: If URL format is invalid or required data is missing
    """
    # Convert URL to XML API endpoint
    if not url.startswith('https://dblp.org/pid/'):
        raise ValueError(f"Invalid DBLP URL format: {url}")

    xml_url = url + '.xml'

    try:
        # Fetch XML data
        response = requests.get(xml_url, timeout=30)
        response.raise_for_status()

        # Parse XML
        root = ET.fromstring(response.content)

        # Extract basic information from root element
        name = root.get('name', '')
        pid = root.get('pid', '')
        publication_count = int(root.get('n', 0))

        # Initialize lists
        urls = []
        affiliations = []
        awards = []
        publications = []

        # Parse person element
        person_elem = root.find('person')
        if person_elem is not None:
            for elem in person_elem:
                if elem.tag == 'url' and elem.text:
                    urls.append(elem.text)
                elif elem.tag == 'note':
                    note_type = elem.get('type')
                    if note_type == 'affiliation' and elem.text:
                        affiliations.append(elem.text)
                    elif note_type == 'award' and elem.text:
                        label = elem.get('label', '')
                        awards.append({
                            'year': label,
                            'award': elem.text
                        })

        # Parse publications
        for r_elem in root.findall('r'):
            pub = _parse_publication_from_xml(r_elem)
            if pub:
                publications.append(pub)

        return AuthorProfile(
            name=name,
            pid=pid,
            urls=urls,
            affiliations=affiliations,
            awards=awards,
            publications=publications,
            publication_count=publication_count
        )

    except requests.RequestException as e:
        raise requests.RequestException(f"Failed to fetch data from {xml_url}: {e}")
    except ET.ParseError as e:
        raise ET.ParseError(f"Failed to parse XML from {xml_url}: {e}")
    except Exception as e:
        raise ValueError(f"Error processing author profile: {e}")